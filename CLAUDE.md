# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-application monorepo containing four distinct applications:
- **IDP** (Identity Provider): Next.js authentication service (port 3000)
- **Main Web**: Core web application with PostgreSQL, Redis, and infrastructure (port 5173)
- **Products**: NestJS backend API service
- **Mobile**: Expo/React Native cross-platform application

## Common Development Commands

### IDP Application (Identity Provider)
```bash
cd idp
npm run dev      # Start development server with Turbopack
npm run build    # Build for production
npm run lint     # Run ESLint
```

### Main Web Application
```bash
cd main-web
npm run dev         # Start development server on port 5173
npm run build       # Build for production
npm run lint        # Run ESLint

# Database commands (Drizzle ORM)
npm run db:generate # Generate migration files from schema changes
npm run db:migrate  # Apply migrations to database
npm run db:push     # Push schema directly (development only)
npm run db:studio   # Open Drizzle Studio GUI

# Infrastructure
docker-compose up   # Start PostgreSQL, Redis, Kafka, MinIO
```

### Products Service (NestJS)
```bash
cd products
npm run start:dev   # Start in watch mode for development
npm run build       # Build for production
npm run lint        # Lint and auto-fix
npm run test        # Run unit tests
npm run test:watch  # Run tests in watch mode
npm run test:e2e    # Run end-to-end tests
```

### Mobile Application (Expo)
```bash
cd mobile
npm start           # Start Expo development server
npm run android     # Run on Android
npm run ios         # Run on iOS
npm run lint        # Run ESLint
```

## High-Level Architecture

### Authentication Flow
The IDP (Identity Provider) handles all authentication across applications:
- OAuth 2.0 with PKCE flow implementation
- Shared authentication between web and mobile apps
- OAuth states and tokens stored in PostgreSQL (main-web)

### Database Architecture
- **PostgreSQL**: Primary database for main-web application
  - Schema defined in `main-web/src/db/schema.ts`
  - OAuth states and tokens tables
  - Managed via Drizzle ORM
- **Redis**: Session management and caching

### Infrastructure Services (Docker Compose)
Located in `main-web/docker-compose.yml`:
- PostgreSQL 16 (port 5432)
- Redis (port 6379)
- Apache Kafka + Zookeeper (ports 9092, 2181)
- MinIO S3-compatible storage (ports 9000, 9001)

### Shared Technologies
- **TypeScript**: All applications use TypeScript
- **React 19**: Latest React version across web and mobile
- **Tailwind CSS**: UI styling (v4 for web, NativeWind for mobile)
- **Next.js 15.3.4**: Both IDP and main-web applications
- **Turbopack**: Fast bundler for Next.js development

### Key Integration Points
1. **OAuth Integration**: Main-web integrates with IDP for authentication
   - Client implementation in `main-web/src/lib/oauth-client.ts`
   - Database schema for OAuth states/tokens

2. **Shared UI Patterns**: 
   - Web apps use Radix UI + class-variance-authority
   - Mobile uses React Native UI primitives with similar patterns

3. **API Communication**: Products service provides backend APIs consumed by web/mobile

## Development Notes

- The project uses npm workspaces structure but each app has independent package.json
- All Next.js apps use Turbopack for faster development builds
- Mobile app uses Expo managed workflow for easier development
- Database migrations should be generated and applied when changing schema
- Docker Compose services must be running for main-web development