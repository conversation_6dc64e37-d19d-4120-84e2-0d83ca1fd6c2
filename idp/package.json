{"name": "idp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/shared/db/seed.ts"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@sinclair/typebox": "^0.34.37", "ajv": "^8.17.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.1", "drizzle-orm": "^0.44.2", "lucide-react": "^0.525.0", "next": "15.3.4", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.4", "tailwindcss": "^4", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}