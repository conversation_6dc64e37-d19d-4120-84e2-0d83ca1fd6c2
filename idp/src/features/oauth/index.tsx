import { NextRequest, NextResponse } from "next/server"
import { Static, Type } from '@sinclair/typebox'
import { ajvInstance } from "@/shared/lib/json-schema"
import { generateAuthorizationCode } from "@/shared/lib/tokens"
import { addAuthorizationCode } from "./token"

const AuthorizeRequestSchema = Type.Object({
    scope: Type.Optional(Type.String()), // optional
    response_type: Type.Literal('code'),
    client_id: Type.String(),
    redirect_uri: Type.String(),
    code_challenge: Type.String(),
    code_challenge_method: Type.Literal('S256'),
    state: Type.Optional(Type.String()) // optional state parameter
})

type AuthorizeRequestQuery = Static<typeof AuthorizeRequestSchema>

const validate = ajvInstance.compile(AuthorizeRequestSchema)

// Список разрешенных клиентов (в продакшене должно быть в БД)
const ALLOWED_CLIENTS = {
    "main-web": {
        redirectUris: ["http://localhost:5174/oauth/callback"],
        scopes: ["openid", "profile", "email"]
    }
}

export const GET_AUTHORIZE = async (req: NextRequest) => {
    const params = Object.fromEntries(req.nextUrl.searchParams.entries()) as AuthorizeRequestQuery

    const isValid = validate(params)

    if (!isValid) {
        return NextResponse.json({
            error: 'invalid_request',
            error_description: 'Invalid request parameters'
        }, { status: 400 })
    }

    // Проверяем клиента
    const client = ALLOWED_CLIENTS[params.client_id as keyof typeof ALLOWED_CLIENTS]
    if (!client) {
        return NextResponse.json({
            error: 'invalid_client',
            error_description: 'Unknown client'
        }, { status: 400 })
    }

    // Проверяем redirect_uri
    if (!client.redirectUris.includes(params.redirect_uri)) {
        return NextResponse.json({
            error: 'invalid_request',
            error_description: 'Invalid redirect_uri'
        }, { status: 400 })
    }

    // Проверяем scope
    const requestedScopes = (params.scope || "openid").split(" ")
    const invalidScopes = requestedScopes.filter(scope => !client.scopes.includes(scope))
    if (invalidScopes.length > 0) {
        return NextResponse.json({
            error: 'invalid_scope',
            error_description: `Invalid scopes: ${invalidScopes.join(", ")}`
        }, { status: 400 })
    }

    // TODO: Проверить аутентификацию пользователя
    // Пока что используем фиктивного пользователя
    const userId = "test-user-123"

    // Генерируем код авторизации
    const authCode = generateAuthorizationCode()
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 минут

    // Сохраняем код авторизации
    addAuthorizationCode(authCode, {
        codeChallenge: params.code_challenge,
        codeChallengeMethod: params.code_challenge_method,
        clientId: params.client_id,
        redirectUri: params.redirect_uri,
        scope: params.scope || "openid",
        state: params.state,
        userId,
        expiresAt
    })

    console.log("Generated authorization code:", authCode)

    // Формируем URL для перенаправления
    const redirectUrl = new URL(params.redirect_uri)
    redirectUrl.searchParams.set("code", authCode)
    if (params.state) {
        redirectUrl.searchParams.set("state", params.state)
    }

    // Перенаправляем пользователя
    return NextResponse.redirect(redirectUrl.toString())
}
