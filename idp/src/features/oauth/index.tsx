import { NextRequest, NextResponse } from "next/server"
import { Static, Type } from '@sinclair/typebox'
import { ajvInstance } from "@/shared/lib/json-schema"

const AuthorizeRequestSchema = Type.Object({
    scope: Type.String(), // optional
    response_type: Type.Literal('code'),
    client_id: Type.String(),
    redirect_uri: Type.String(),
    code_challenge: Type.String(),
    code_challenge_method: Type.Literal('S256')
})

type AuthorizeRequestQuery = Static<typeof AuthorizeRequestSchema>

const validate = ajvInstance.compile(AuthorizeRequestSchema)

export const GET_AUTHORIZE = (req: NextRequest) => {

    const params = Object.fromEntries(req.nextUrl.searchParams.entries()) as AuthorizeRequestQuery

    const isValid = validate(params)

    if (!isValid) {
        return NextResponse.json({
            message: 'Invalid request'
        }, { status: 400 })
    }

    return NextResponse.json({
        message: 'Hello, world!'
    })
} 