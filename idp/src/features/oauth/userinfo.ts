import { NextRequest, NextResponse } from "next/server"

// Временное хранилище токенов (в продакшене должно быть в БД)
const accessTokens = new Map<string, {
    userId: string
    clientId: string
    scope: string
    expiresAt: Date
}>()

export const GET_USERINFO = async (req: NextRequest) => {
    try {
        // Получаем токен из заголовка Authorization
        const authHeader = req.headers.get('Authorization')
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return NextResponse.json({
                error: 'invalid_token',
                error_description: 'Missing or invalid authorization header'
            }, { status: 401 })
        }

        const accessToken = authHeader.substring(7) // Убираем "Bearer "
        
        // Проверяем токен
        const tokenData = accessTokens.get(accessToken)
        if (!tokenData) {
            return NextResponse.json({
                error: 'invalid_token',
                error_description: 'Invalid access token'
            }, { status: 401 })
        }

        // Проверяем, не истек ли токен
        if (tokenData.expiresAt < new Date()) {
            accessTokens.delete(accessToken)
            return NextResponse.json({
                error: 'invalid_token',
                error_description: 'Access token expired'
            }, { status: 401 })
        }

        // Получаем информацию о пользователе (заглушка)
        // В продакшене здесь должен быть запрос к базе данных пользователей
        const userInfo = {
            sub: tokenData.userId,
            email: "<EMAIL>",
            email_verified: true,
            name: "Test User",
            given_name: "Test",
            family_name: "User",
            picture: "https://via.placeholder.com/150",
            locale: "en"
        }

        // Фильтруем информацию по scope
        const scopes = tokenData.scope.split(' ')
        const filteredUserInfo: any = { sub: userInfo.sub }

        if (scopes.includes('email')) {
            filteredUserInfo.email = userInfo.email
            filteredUserInfo.email_verified = userInfo.email_verified
        }

        if (scopes.includes('profile')) {
            filteredUserInfo.name = userInfo.name
            filteredUserInfo.given_name = userInfo.given_name
            filteredUserInfo.family_name = userInfo.family_name
            filteredUserInfo.picture = userInfo.picture
            filteredUserInfo.locale = userInfo.locale
        }

        return NextResponse.json(filteredUserInfo)

    } catch (error) {
        console.error("Userinfo endpoint error:", error)
        return NextResponse.json({
            error: 'server_error',
            error_description: 'Internal server error'
        }, { status: 500 })
    }
}

// Функция для добавления access token (будет вызываться из token эндпоинта)
export function addAccessToken(token: string, data: {
    userId: string
    clientId: string
    scope: string
    expiresAt: Date
}) {
    accessTokens.set(token, data)
}
