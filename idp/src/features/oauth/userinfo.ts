import { NextRequest, NextResponse } from "next/server"
import { db } from "@/shared/db"
import { oauthTokens, users } from "@/shared/db/schema"
import { eq } from "drizzle-orm"

export const GET_USERINFO = async (req: NextRequest) => {
    try {
        // Получаем токен из заголовка Authorization
        const authHeader = req.headers.get('Authorization')
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return NextResponse.json({
                error: 'invalid_token',
                error_description: 'Missing or invalid authorization header'
            }, { status: 401 })
        }

        const accessToken = authHeader.substring(7) // Убираем "Bearer "

        // Проверяем токен в базе данных
        const tokenResult = await db.select()
            .from(oauthTokens)
            .where(eq(oauthTokens.accessToken, accessToken))
            .limit(1)

        if (tokenResult.length === 0) {
            return NextResponse.json({
                error: 'invalid_token',
                error_description: 'Invalid access token'
            }, { status: 401 })
        }

        const tokenData = tokenResult[0]

        // Проверяем, не истек ли токен
        if (tokenData.expiresAt < new Date()) {
            // Удаляем истекший токен
            await db.delete(oauthTokens)
                .where(eq(oauthTokens.accessToken, accessToken))

            return NextResponse.json({
                error: 'invalid_token',
                error_description: 'Access token expired'
            }, { status: 401 })
        }

        // Получаем информацию о пользователе из базы данных
        const userResult = await db.select()
            .from(users)
            .where(eq(users.id, tokenData.userId))
            .limit(1)

        const user = userResult[0] || {
            id: tokenData.userId,
            email: "<EMAIL>",
            name: "Test User",
            createdAt: new Date(),
            updatedAt: new Date()
        }

        const userInfo = {
            sub: tokenData.userId,
            email: user.email,
            email_verified: true,
            name: user.name,
            given_name: user.name.split(' ')[0] || user.name,
            family_name: user.name.split(' ')[1] || '',
            picture: "https://via.placeholder.com/150",
            locale: "en"
        }

        // Фильтруем информацию по scope
        const scopes = tokenData.scope.split(' ')
        const filteredUserInfo: any = { sub: userInfo.sub }

        if (scopes.includes('email')) {
            filteredUserInfo.email = userInfo.email
            filteredUserInfo.email_verified = userInfo.email_verified
        }

        if (scopes.includes('profile')) {
            filteredUserInfo.name = userInfo.name
            filteredUserInfo.given_name = userInfo.given_name
            filteredUserInfo.family_name = userInfo.family_name
            filteredUserInfo.picture = userInfo.picture
            filteredUserInfo.locale = userInfo.locale
        }

        return NextResponse.json(filteredUserInfo)

    } catch (error) {
        console.error("Userinfo endpoint error:", error)
        return NextResponse.json({
            error: 'server_error',
            error_description: 'Internal server error'
        }, { status: 500 })
    }
}


