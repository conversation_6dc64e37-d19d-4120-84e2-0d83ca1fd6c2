import { NextRequest, NextResponse } from "next/server"
import { Static, Type } from '@sinclair/typebox'
import { ajvInstance } from "@/shared/lib/json-schema"
import { verifyPKCE } from "@/shared/lib/pkce"
import { generateAccessToken, generateRefreshToken, generateIdToken } from "@/shared/lib/tokens"
import { addAccessToken } from "./userinfo"

const TokenRequestSchema = Type.Object({
    grant_type: Type.Literal('authorization_code'),
    code: Type.String(),
    redirect_uri: Type.String(),
    client_id: Type.String(),
    code_verifier: Type.String()
})

type TokenRequestBody = Static<typeof TokenRequestSchema>

const validate = ajvInstance.compile(TokenRequestSchema)

// Временное хранилище кодов авторизации (в продакшене должно быть в БД)
const authorizationCodes = new Map<string, {
    codeChallenge: string
    codeChallengeMethod: string
    clientId: string
    redirectUri: string
    scope: string
    state?: string
    userId: string
    expiresAt: Date
    used: boolean
}>()

export const POST_TOKEN = async (req: NextRequest) => {
    try {
        const body = await req.json() as TokenRequestBody
        
        const isValid = validate(body)
        
        if (!isValid) {
            return NextResponse.json({
                error: 'invalid_request',
                error_description: 'Invalid request parameters'
            }, { status: 400 })
        }

        // Получаем код авторизации
        const authCode = authorizationCodes.get(body.code)
        if (!authCode) {
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Invalid authorization code'
            }, { status: 400 })
        }

        // Проверяем, не истек ли код
        if (authCode.expiresAt < new Date()) {
            authorizationCodes.delete(body.code)
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Authorization code expired'
            }, { status: 400 })
        }

        // Проверяем, не был ли код уже использован
        if (authCode.used) {
            authorizationCodes.delete(body.code)
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Authorization code already used'
            }, { status: 400 })
        }

        // Проверяем client_id
        if (authCode.clientId !== body.client_id) {
            return NextResponse.json({
                error: 'invalid_client',
                error_description: 'Client ID mismatch'
            }, { status: 400 })
        }

        // Проверяем redirect_uri
        if (authCode.redirectUri !== body.redirect_uri) {
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Redirect URI mismatch'
            }, { status: 400 })
        }

        // Проверяем PKCE
        if (!verifyPKCE(body.code_verifier, authCode.codeChallenge, authCode.codeChallengeMethod)) {
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'PKCE verification failed'
            }, { status: 400 })
        }

        // Помечаем код как использованный
        authCode.used = true

        // Генерируем токены
        const accessToken = generateAccessToken()
        const refreshToken = generateRefreshToken()
        
        // Получаем информацию о пользователе (заглушка)
        const userInfo = {
            sub: authCode.userId,
            email: "<EMAIL>",
            name: "Test User",
            given_name: "Test",
            family_name: "User",
            picture: "https://via.placeholder.com/150"
        }
        
        const idToken = generateIdToken(authCode.userId, authCode.clientId, userInfo)
        
        const expiresIn = 3600 // 1 час
        const tokenExpiresAt = new Date(Date.now() + expiresIn * 1000)

        // Сохраняем access token
        addAccessToken(accessToken, {
            userId: authCode.userId,
            clientId: authCode.clientId,
            scope: authCode.scope,
            expiresAt: tokenExpiresAt
        })

        console.log("Generated tokens for user:", authCode.userId)

        // Возвращаем токены
        return NextResponse.json({
            access_token: accessToken,
            token_type: "Bearer",
            expires_in: expiresIn,
            refresh_token: refreshToken,
            id_token: idToken,
            scope: authCode.scope
        })

    } catch (error) {
        console.error("Token endpoint error:", error)
        return NextResponse.json({
            error: 'server_error',
            error_description: 'Internal server error'
        }, { status: 500 })
    }
}

// Функция для добавления кода авторизации (будет вызываться из authorize эндпоинта)
export function addAuthorizationCode(code: string, data: {
    codeChallenge: string
    codeChallengeMethod: string
    clientId: string
    redirectUri: string
    scope: string
    state?: string
    userId: string
    expiresAt: Date
}) {
    authorizationCodes.set(code, {
        ...data,
        used: false
    })
}
