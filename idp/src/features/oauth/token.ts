import { NextRequest, NextResponse } from "next/server"
import { Static, Type } from '@sinclair/typebox'
import { ajvInstance } from "@/shared/lib/json-schema"
import { verifyPKCE } from "@/shared/lib/pkce"
import { generateAccessToken, generateRefreshToken, generateIdToken } from "@/shared/lib/tokens"
import { db } from "@/shared/db"
import { oauthAuthorizationCodes, oauthTokens, users } from "@/shared/db/schema"
import { eq, and } from "drizzle-orm"

const TokenRequestSchema = Type.Object({
    grant_type: Type.Literal('authorization_code'),
    code: Type.String(),
    redirect_uri: Type.String(),
    client_id: Type.String(),
    code_verifier: Type.String()
})

type TokenRequestBody = Static<typeof TokenRequestSchema>

const validate = ajvInstance.compile(TokenRequestSchema)



export const POST_TOKEN = async (req: NextRequest) => {
    try {
        // OAuth 2.0 token requests должны быть в формате application/x-www-form-urlencoded
        const formData = await req.formData()
        const body = {
            grant_type: formData.get('grant_type') as string,
            code: formData.get('code') as string,
            redirect_uri: formData.get('redirect_uri') as string,
            client_id: formData.get('client_id') as string,
            code_verifier: formData.get('code_verifier') as string,
        } as TokenRequestBody
        
        const isValid = validate(body)
        
        if (!isValid) {
            return NextResponse.json({
                error: 'invalid_request',
                error_description: 'Invalid request parameters'
            }, { status: 400 })
        }

        // Получаем код авторизации из базы данных
        const authCodeResult = await db.select()
            .from(oauthAuthorizationCodes)
            .where(eq(oauthAuthorizationCodes.code, body.code))
            .limit(1)

        if (authCodeResult.length === 0) {
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Invalid authorization code'
            }, { status: 400 })
        }

        const authCode = authCodeResult[0]

        // Проверяем, не истек ли код
        if (authCode.expiresAt < new Date()) {
            // Удаляем истекший код
            await db.delete(oauthAuthorizationCodes)
                .where(eq(oauthAuthorizationCodes.code, body.code))

            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Authorization code expired'
            }, { status: 400 })
        }

        // Проверяем, не был ли код уже использован
        if (authCode.used) {
            // Удаляем использованный код
            await db.delete(oauthAuthorizationCodes)
                .where(eq(oauthAuthorizationCodes.code, body.code))

            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Authorization code already used'
            }, { status: 400 })
        }

        // Проверяем client_id
        if (authCode.clientId !== body.client_id) {
            return NextResponse.json({
                error: 'invalid_client',
                error_description: 'Client ID mismatch'
            }, { status: 400 })
        }

        // Проверяем redirect_uri
        if (authCode.redirectUri !== body.redirect_uri) {
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'Redirect URI mismatch'
            }, { status: 400 })
        }

        // Проверяем PKCE
        if (!verifyPKCE(body.code_verifier, authCode.codeChallenge, authCode.codeChallengeMethod)) {
            return NextResponse.json({
                error: 'invalid_grant',
                error_description: 'PKCE verification failed'
            }, { status: 400 })
        }

        // Помечаем код как использованный в базе данных
        await db.update(oauthAuthorizationCodes)
            .set({ used: true })
            .where(eq(oauthAuthorizationCodes.code, body.code))

        // Генерируем токены
        const accessToken = generateAccessToken()
        const refreshToken = generateRefreshToken()

        // Получаем информацию о пользователе из базы данных
        const userResult = await db.select()
            .from(users)
            .where(eq(users.id, authCode.userId))
            .limit(1)

        const user = userResult[0] || {
            id: authCode.userId,
            email: "<EMAIL>",
            name: "Test User",
            createdAt: new Date(),
            updatedAt: new Date()
        }

        const userName = user.name || user.email
        const userInfo = {
            sub: authCode.userId,
            email: user.email,
            name: userName,
            given_name: userName.split(' ')[0] || userName,
            family_name: userName.split(' ')[1] || '',
            picture: "https://via.placeholder.com/150"
        }

        const idToken = generateIdToken(authCode.userId, authCode.clientId, userInfo)

        const expiresIn = 3600 // 1 час
        const tokenExpiresAt = new Date(Date.now() + expiresIn * 1000)

        // Сохраняем токены в базу данных
        await db.insert(oauthTokens).values({
            accessToken,
            refreshToken,
            userId: authCode.userId,
            clientId: authCode.clientId,
            scope: authCode.scope,
            expiresIn,
            expiresAt: tokenExpiresAt,
            createdAt: new Date()
        })

        console.log("Generated tokens for user:", authCode.userId)

        // Возвращаем токены
        return NextResponse.json({
            access_token: accessToken,
            token_type: "Bearer",
            expires_in: expiresIn,
            refresh_token: refreshToken,
            id_token: idToken,
            scope: authCode.scope
        })

    } catch (error) {
        console.error("Token endpoint error:", error)
        return NextResponse.json({
            error: 'server_error',
            error_description: 'Internal server error'
        }, { status: 500 })
    }
}

// Функция для добавления кода авторизации (будет вызываться из authorize эндпоинта)
export async function addAuthorizationCode(code: string, data: {
    codeChallenge: string
    codeChallengeMethod: string
    clientId: string
    redirectUri: string
    scope: string
    state?: string
    userId: string
    expiresAt: Date
}) {
    await db.insert(oauthAuthorizationCodes).values({
        code,
        codeChallenge: data.codeChallenge,
        codeChallengeMethod: data.codeChallengeMethod,
        clientId: data.clientId,
        redirectUri: data.redirectUri,
        scope: data.scope,
        state: data.state,
        userId: data.userId,
        expiresAt: data.expiresAt,
        used: false,
        createdAt: new Date()
    })
}
