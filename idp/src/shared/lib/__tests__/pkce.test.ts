import { verifyPK<PERSON> } from "../pkce";
import { createHash } from "node:crypto";

describe("PKCE Verification", () => {
  test("should verify valid PKCE pair", () => {
    const codeVerifier = "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk";
    
    // Generate challenge manually to test
    const hash = createHash("sha256").update(codeVerifier).digest("base64");
    const codeChallenge = hash
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=+$/, "");
    
    const result = verifyPKCE(codeVerifier, codeChallenge, "S256");
    expect(result).toBe(true);
  });

  test("should reject invalid PKCE pair", () => {
    const codeVerifier = "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk";
    const wrongChallenge = "wrong-challenge";
    
    const result = verifyPKCE(codeVerifier, wrongChallenge, "S256");
    expect(result).toBe(false);
  });

  test("should throw error for unsupported method", () => {
    const codeVerifier = "test";
    const codeChallenge = "test";
    
    expect(() => {
      verifyPKCE(codeVerifier, codeChallenge, "plain");
    }).toThrow("Only S256 method is supported");
  });
});
