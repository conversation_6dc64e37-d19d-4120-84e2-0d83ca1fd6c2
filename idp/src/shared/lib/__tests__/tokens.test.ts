import { generateAuthorizationCode, generateAccessToken, generateRefreshToken } from "@/shared/lib/tokens";

describe("Token Generation", () => {
  test("should generate authorization code", () => {
    const code = generateAuthorizationCode();
    expect(code).toBeTruthy();
    expect(typeof code).toBe("string");
    expect(code.length).toBeGreaterThan(10);
  });

  test("should generate access token", () => {
    const token = generateAccessToken();
    expect(token).toBeTruthy();
    expect(typeof token).toBe("string");
    expect(token.length).toBeGreaterThan(10);
  });

  test("should generate refresh token", () => {
    const token = generateRefreshToken();
    expect(token).toBeTruthy();
    expect(typeof token).toBe("string");
    expect(token.length).toBeGreaterThan(10);
  });
});


