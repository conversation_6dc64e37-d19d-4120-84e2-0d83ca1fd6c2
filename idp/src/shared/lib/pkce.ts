import { createHash } from "node:crypto";

/**
 * Проверяет PKCE code_verifier против code_challenge
 */
export function verifyPKCE(codeVerifier: string, codeChallenge: string, method: string = "S256"): boolean {
  if (method !== "S256") {
    throw new Error("Only S256 method is supported");
  }
  
  const hash = createHash("sha256").update(codeVerifier).digest("base64");
  const challenge = hash
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, ""); // Clean base64 to make it URL safe
    
  return challenge === codeChallenge;
}
