import { randomBytes } from "node:crypto";

/**
 * Генерирует случайный код авторизации
 */
export function generateAuthorizationCode(): string {
  return randomBytes(32).toString("hex");
}

/**
 * Генерирует access token
 */
export function generateAccessToken(): string {
  return randomBytes(32).toString("hex");
}

/**
 * Генерирует refresh token
 */
export function generateRefreshToken(): string {
  return randomBytes(32).toString("hex");
}

/**
 * Создает простой JWT-подобный ID token (для демонстрации)
 * В продакшене следует использовать полноценную JWT библиотеку
 */
export function generateIdToken(userId: string, clientId: string, userInfo: any): string {
  const header = {
    alg: "none",
    typ: "JWT"
  };
  
  const payload = {
    iss: process.env.NEXTAUTH_URL || "http://localhost:3000",
    sub: userId,
    aud: clientId,
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 час
    iat: Math.floor(Date.now() / 1000),
    ...userInfo
  };
  
  const encodedHeader = Buffer.from(JSON.stringify(header)).toString("base64url");
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString("base64url");
  
  return `${encodedHeader}.${encodedPayload}.`;
}
