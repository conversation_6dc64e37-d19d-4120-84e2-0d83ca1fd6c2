import { pgTable, text, timestamp, uuid, boolean } from "drizzle-orm/pg-core";

export const oauthClients = pgTable("oauth_clients", {
  id: uuid("id").primaryKey().defaultRandom(),
  clientId: text("client_id").unique().notNull(),
  clientSecret: text("client_secret"), // Для конфиденциальных клиентов
  name: text("name").notNull(),
  
  // Настройки клиента
  redirectUris: text("redirect_uris").notNull(), // JSON массив разрешенных redirect_uri
  scopes: text("scopes").notNull().default("openid profile email"), // Разрешенные scopes
  grantTypes: text("grant_types").notNull().default("authorization_code"), // Разрешенные grant types
  
  // Тип клиента
  isPublic: boolean("is_public").default(true), // Публичный клиент (мобильные, SPA)
  requirePkce: boolean("require_pkce").default(true), // Требует PKCE
  
  // Время жизни токенов для этого клиента
  accessTokenTtl: text("access_token_ttl").default("3600"), // В секундах
  refreshTokenTtl: text("refresh_token_ttl").default("2592000"), // 30 дней
  
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export type OAuthClient = typeof oauthClients.$inferSelect;
export type NewOAuthClient = typeof oauthClients.$inferInsert;
