import { pgTable, text, timestamp, uuid, integer } from "drizzle-orm/pg-core";

export const oauthTokens = pgTable("oauth_tokens", {
  id: uuid("id").primaryKey().defaultRandom(),
  
  // Токены
  accessToken: text("access_token").unique().notNull(),
  refreshToken: text("refresh_token").unique(),
  idToken: text("id_token"), // OpenID Connect ID token
  
  // Метаданные токенов
  tokenType: text("token_type").notNull().default("Bearer"),
  scope: text("scope").notNull(),
  expiresIn: integer("expires_in").notNull(), // Время жизни access token в секундах
  expiresAt: timestamp("expires_at").notNull(), // Когда истекает access token
  
  // Связи
  clientId: text("client_id").notNull(),
  userId: text("user_id").notNull(), // ID пользователя
  
  // Для refresh token
  refreshExpiresAt: timestamp("refresh_expires_at"), // Когда истекает refresh token
  
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export type OAuthToken = typeof oauthTokens.$inferSelect;
export type NewOAuthToken = typeof oauthTokens.$inferInsert;
