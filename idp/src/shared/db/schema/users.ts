import { pgTable, text, timestamp, uuid, boolean } from "drizzle-orm/pg-core";

export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  
  // Основная информация
  email: text("email").unique().notNull(),
  username: text("username").unique(),
  name: text("name"),
  
  // Аутентификация
  passwordHash: text("password_hash"), // Хеш пароля
  emailVerified: boolean("email_verified").default(false),
  
  // OpenID Connect claims
  givenName: text("given_name"),
  familyName: text("family_name"),
  picture: text("picture"), // URL аватара
  locale: text("locale").default("en"),
  
  // Статус
  isActive: boolean("is_active").default(true),
  
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
