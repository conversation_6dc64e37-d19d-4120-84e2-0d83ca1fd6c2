import { pgTable, text, timestamp, uuid, boolean } from "drizzle-orm/pg-core";

export const oauthAuthorizationCodes = pgTable("oauth_authorization_codes", {
  id: uuid("id").primaryKey().defaultRandom(),
  code: text("code").unique().notNull(),
  
  // PKCE параметры
  codeChallenge: text("code_challenge").notNull(),
  codeChallengeMethod: text("code_challenge_method").notNull().default("S256"),
  
  // OAuth параметры
  clientId: text("client_id").notNull(),
  redirectUri: text("redirect_uri").notNull(),
  scope: text("scope").notNull().default("openid profile email"),
  state: text("state"), // Опциональный state параметр
  
  // Пользователь
  userId: text("user_id").notNull(), // ID пользователя, который авторизовался
  
  // Статус и время жизни
  used: boolean("used").default(false), // Был ли использован код
  expiresAt: timestamp("expires_at").notNull(), // Время истечения (обычно 10 минут)
  
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export type OAuthAuthorizationCode = typeof oauthAuthorizationCodes.$inferSelect;
export type NewOAuthAuthorizationCode = typeof oauthAuthorizationCodes.$inferInsert;
