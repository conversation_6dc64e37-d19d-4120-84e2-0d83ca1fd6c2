import { config } from "dotenv"
import { db } from "./index"
import { users, oauthClients } from "./schema"

// Загружаем переменные окружения
config({ path: ".env.development" })

export async function seedDatabase() {
  try {
    console.log("🌱 Seeding database...")

    // Создаем тестового пользователя
    await db.insert(users).values([
      {
        email: "<EMAIL>",
        name: "Test User",
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        email: "<EMAIL>",
        name: "Admin User",
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]).onConflictDoNothing()

    // Создаем OAuth клиента
    await db.insert(oauthClients).values([
      {
        clientId: "main-web",
        name: "Main Web Application",
        redirectUris: JSON.stringify(["http://localhost:5174/oauth/callback"]),
        scopes: "openid profile email",
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]).onConflictDoNothing()

    console.log("✅ Database seeded successfully!")
  } catch (error) {
    console.error("❌ Error seeding database:", error)
    throw error
  }
}

// Запускаем seed если файл вызывается напрямую
if (require.main === module) {
  seedDatabase()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}
