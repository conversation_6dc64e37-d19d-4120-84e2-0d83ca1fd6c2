services:
  pg:
    image: postgres:16
    container_name: main-web-pg
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - main-web-pg-vol:/var/lib/postgresql/data
      - ./libs/database/pg-init/:/docker-entrypoint-initdb.d/

  redis:
    image: redis
    container_name: main-web-redis
    command: redis-server --loglevel warning
    ports:
      - "6379:6379"
    volumes:
      - main-web-redis-vol:/data

  kafka:
    image: docker.io/bitnami/kafka:latest
    container_name: main-web-kafka
    ports:
      - "9092:9092"
    environment:
      - KAFKA_CFG_NODE_ID=1
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_ENABLE_KRAFT=yes
      - KAFKA_CFG_PROCESS_ROLES=broker,controller
      - KAFKA_BROKER_ID=1
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@127.0.0.1:9093
      - <PERSON><PERSON><PERSON>_CFG_LISTENERS=BROKER://:9092,CONTROLLER://:9093
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=BROKER:SASL_PLAINTEXT,CONTROLLER:PLAINTEXT
      - KAFKA_CFG_ADVERTISED_LISTENERS=BROKER://localhost:9092
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=BROKER
      - KAFKA_CFG_SASL_ENABLED_MECHANISMS=PLAIN
      - KAFKA_CFG_SASL_MECHANISM_INTER_BROKER_PROTOCOL=PLAIN
      - KAFKA_CFG_SASL_MECHANISM_CONTROLLER_PROTOCOL=PLAIN
      - KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
      - KAFKA_DELETE_TOPIC_ENABLE=true
      - KAFKA_CFG_DEFAULT_REPLICATION_FACTOR=1
      - KAFKA_CFG_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_CFG_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
      - KAFKA_CFG_TRANSACTION_STATE_LOG_MIN_ISR=1
      - KAFKA_CFG_LISTENER_NAME_BROKER_PLAIN_SASL_JAAS_CONFIG=org.apache.kafka.common.security.plain.PlainLoginModule required username="client" password="client-secret" user_client="client-secret";
      - KAFKA_CFG_LISTENER_NAME_CONTROLLER_PLAIN_SASL_JAAS_CONFIG=org.apache.kafka.common.security.plain.PlainLoginModule required username="client" password="client-secret" user_client="client-secret";
      - BITNAMI_DEBUG=true
    volumes:
      - ./kafka_client.properties:/tmp/client.properties
      - main-web-kafka-vol:/bitnami/kafka

  minio:
    image: quay.io/minio/minio:RELEASE.2024-06-11T03-13-30Z
    container_name: main-web-minio
    command: server --console-address ":9001" /data/
    env_file:
      - .env.development
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minio_admin
      MINIO_ROOT_PASSWORD: minio_passwd

  createS3Buckets:
    image: minio/mc
    container_name: main-web-s3bucket
    depends_on:
      - minio
    env_file:
      - .env.development
    entrypoint: >
      /bin/sh -c "
          until (/usr/bin/mc alias set local http://minio:9000 minio_admin minio_passwd;) do echo '...waiting...' &&
      sleep 1;
      done;
      /usr/bin/mc admin user add local $${S3_ACCESS_KEY} $${S3_SECRET_ACCESS_KEY};
      /usr/bin/mc admin policy attach local readwrite --user=$${S3_ACCESS_KEY};
      /usr/bin/mc mb local/$${S3_BUCKET_NAME};
      /usr/bin/mc anonymous set download local/$${S3_BUCKET_NAME};
      exit 0;
      "

volumes:
  main-web-pg-vol:
  main-web-redis-vol:
  main-web-kafka-vol:
