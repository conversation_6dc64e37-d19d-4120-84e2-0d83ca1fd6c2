{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 5173", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@types/pg": "^8.15.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "ioredis": "^5.6.1", "lucide-react": "^0.525.0", "next": "15.3.4", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/ioredis": "^4.28.10", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}