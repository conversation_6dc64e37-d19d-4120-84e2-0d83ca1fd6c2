"use server";

import { db, oauthStates } from "@/shared/db";
import { generatePKCEPair } from "./pkce";
import { redirect } from "next/navigation";
import { redisClient, redisGetJson, redisSetJson } from "../redis";

type OAuthState = {
  state: string;
  codeVerifier: string;
  codeChallenge: string;
  codeChallengeMethod: string;
  redirectUri: string;
  clientId: string;
  scope: string;
  used: boolean;
};

export const authorizeAction = async () => {
  const url = new URL(process.env.NEXT_PUBLIC_IDP_URL);

  const { verifier, challenge } = generatePKCEPair();
  const state = crypto.randomUUID();

  const oauthState = {
    state,
    codeVerifier: verifier,
    codeChallenge: challenge,
    codeChallengeMethod: "S256",
    redirectUri: `${process.env.NEXT_PUBLIC_PUBLIC_URL}/oauth/callback`,
    clientId: "main-web",
    scope: "openid profile email",
    used: false,
  } satisfies OAuthState;

  await redisSet<PERSON><PERSON>(`oauth_state:${state}`, oauthState, 60 * 15);

  url.pathname = "/authorize";
  url.searchParams.set("response_type", "code");
  url.searchParams.set("client_id", oauthState.clientId);
  url.searchParams.set("redirect_uri", oauthState.redirectUri);
  url.searchParams.set("scope", oauthState.scope);
  url.searchParams.set("code_challenge", oauthState.codeChallenge);
  url.searchParams.set("code_challenge_method", oauthState.codeChallengeMethod);
  url.searchParams.set("state", oauthState.state);

  const res = await redisGetJson<OAuthState>(`oauth_state:${state}`);
  console.log(res);

  redirect(url.toString());
};
