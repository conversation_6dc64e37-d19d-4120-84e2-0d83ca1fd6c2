import Redis from "ioredis";

export const redisClient = new Redis(process.env.REDIS_URL!);

export const redisSetJson = async (key: string, value: any, ttl: number) => {
  await redisClient.set(key, JSON.stringify(value), "EX", ttl);
};

export const redisGetJson = async <T>(key: string): Promise<T | null> => {
  const value = await redisClient.get(key);
  return value ? (JSON.parse(value) as T) : null;
};
