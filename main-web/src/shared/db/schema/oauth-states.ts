import { pgTable, text, timestamp, uuid, boolean } from "drizzle-orm/pg-core";

export const oauthStates = pgTable("oauth_states", {
  id: uuid("id").primaryKey().defaultRandom(),
  state: text("state").unique().notNull(),
  codeVerifier: text("code_verifier").notNull(),
  codeChallenge: text("code_challenge").notNull(),
  codeChallengeMethod: text("code_challenge_method").notNull().default("S256"),
  redirectUri: text("redirect_uri").notNull(),
  scope: text("scope").notNull().default("openid profile email"),
  clientId: text("client_id").notNull(),

  // Статус и время жизни
  used: boolean("used").default(false), // Был ли использован
  expiresAt: timestamp("expires_at").notNull(), // Время истечения (обычно 10-15 минут)

  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export type OAuthState = typeof oauthStates.$inferSelect;
export type NewOAuthState = typeof oauthStates.$inferInsert;
