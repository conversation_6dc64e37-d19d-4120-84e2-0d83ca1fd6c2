import { pgTable, text, timestamp, uuid, integer } from "drizzle-orm/pg-core";

export const oauthTokens = pgTable("oauth_tokens", {
  id: uuid("id").primaryKey().defaultRandom(),

  // Токены
  accessToken: text("access_token").notNull(),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"), // OpenID Connect ID token

  // Метаданные токенов
  tokenType: text("token_type").notNull().default("Bearer"),
  scope: text("scope").notNull(),
  expiresIn: integer("expires_in"), // Время жизни access token в секундах
  expiresAt: timestamp("expires_at"), // Когда истекает access token

  // Провайдер OAuth
  provider: text("provider").notNull().default("idp"), // Название провайдера
  providerAccountId: text("provider_account_id"), // ID пользователя у провайдера

  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export type OAuthToken = typeof oauthTokens.$inferSelect;
export type NewOAuthToken = typeof oauthTokens.$inferInsert;
