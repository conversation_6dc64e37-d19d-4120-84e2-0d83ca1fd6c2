"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { redisGetJson } from "@/shared/lib/redis";

type OAuthState = {
  state: string;
  codeVerifier: string;
  codeChallenge: string;
  codeChallengeMethod: string;
  redirectUri: string;
  clientId: string;
  scope: string;
  used: boolean;
};

export default function OAuthCallback() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading");
  const [error, setError] = useState<string>("");
  const [tokens, setTokens] = useState<any>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get("code");
        const state = searchParams.get("state");
        const error = searchParams.get("error");

        if (error) {
          setError(`OAuth error: ${error}`);
          setStatus("error");
          return;
        }

        if (!code || !state) {
          setError("Missing code or state parameter");
          setStatus("error");
          return;
        }

        // Получаем сохраненное состояние из Redis
        const response = await fetch("/api/oauth/get-state", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ state })
        });

        if (!response.ok) {
          setError("Failed to retrieve OAuth state");
          setStatus("error");
          return;
        }

        const oauthState: OAuthState = await response.json();

        if (oauthState.used) {
          setError("OAuth state already used");
          setStatus("error");
          return;
        }

        // Обмениваем код на токены
        const tokenResponse = await fetch(`${process.env.NEXT_PUBLIC_IDP_URL}/token`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            grant_type: "authorization_code",
            code,
            redirect_uri: oauthState.redirectUri,
            client_id: oauthState.clientId,
            code_verifier: oauthState.codeVerifier
          })
        });

        if (!tokenResponse.ok) {
          const errorData = await tokenResponse.json();
          setError(`Token exchange failed: ${errorData.error_description || errorData.error}`);
          setStatus("error");
          return;
        }

        const tokenData = await tokenResponse.json();
        setTokens(tokenData);

        // Получаем информацию о пользователе
        const userinfoResponse = await fetch(`${process.env.NEXT_PUBLIC_IDP_URL}/userinfo`, {
          headers: {
            "Authorization": `Bearer ${tokenData.access_token}`
          }
        });

        if (userinfoResponse.ok) {
          const userInfo = await userinfoResponse.json();
          console.log("User info:", userInfo);
        }

        // Помечаем состояние как использованное
        await fetch("/api/oauth/mark-used", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ state })
        });

        setStatus("success");

        // Перенаправляем на главную страницу через 3 секунды
        setTimeout(() => {
          router.push("/");
        }, 3000);

      } catch (err) {
        console.error("OAuth callback error:", err);
        setError("An unexpected error occurred");
        setStatus("error");
      }
    };

    handleCallback();
  }, [searchParams, router]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-lg">Processing OAuth callback...</p>
        </div>
      </div>
    );
  }

  if (status === "error") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">❌</div>
          <h1 className="text-2xl font-bold text-red-600 mb-2">OAuth Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push("/")}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Go Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="text-green-500 text-6xl mb-4">✅</div>
        <h1 className="text-2xl font-bold text-green-600 mb-2">OAuth Success!</h1>
        <p className="text-gray-600 mb-4">You have been successfully authenticated.</p>
        {tokens && (
          <div className="bg-gray-100 p-4 rounded-lg mb-4 text-left">
            <h3 className="font-bold mb-2">Received Tokens:</h3>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(tokens, null, 2)}
            </pre>
          </div>
        )}
        <p className="text-sm text-gray-500">Redirecting to home page in 3 seconds...</p>
      </div>
    </div>
  );
}
