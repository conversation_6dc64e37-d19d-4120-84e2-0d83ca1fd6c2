import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { accessToken } = await req.json();
    
    if (!accessToken) {
      return NextResponse.json({ error: "Access token required" }, { status: 400 });
    }
    
    // Проксируем запрос к IDP
    const userinfoResponse = await fetch(`${process.env.NEXT_PUBLIC_IDP_URL}/userinfo`, {
      headers: {
        "Authorization": `Bearer ${accessToken}`
      }
    });

    const userInfo = await userinfoResponse.json();
    
    if (!userinfoResponse.ok) {
      return NextResponse.json(userInfo, { status: userinfoResponse.status });
    }

    return NextResponse.json(userInfo);
  } catch (error) {
    console.error("Userinfo error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
