import { NextRequest, NextResponse } from "next/server";
import { redisGetJson } from "@/shared/lib/redis";

export async function POST(req: NextRequest) {
  try {
    const { state } = await req.json();
    
    if (!state) {
      return NextResponse.json({ error: "State parameter required" }, { status: 400 });
    }

    const oauthState = await redisGetJson(`oauth_state:${state}`);
    
    if (!oauthState) {
      return NextResponse.json({ error: "Invalid or expired state" }, { status: 400 });
    }

    return NextResponse.json(oauthState);
  } catch (error) {
    console.error("Get state error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
