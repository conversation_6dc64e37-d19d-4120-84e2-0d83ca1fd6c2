import { NextRequest, NextResponse } from "next/server";
import { redisGetJson, redisSetJson } from "@/shared/lib/redis";

export async function POST(req: NextRequest) {
  try {
    const { state } = await req.json();
    
    if (!state) {
      return NextResponse.json({ error: "State parameter required" }, { status: 400 });
    }

    const oauthState = await redisGetJson(`oauth_state:${state}`);
    
    if (!oauthState) {
      return NextResponse.json({ error: "Invalid or expired state" }, { status: 400 });
    }

    // Помечаем как использованное
    oauthState.used = true;
    await redisSetJson(`oauth_state:${state}`, oauthState, 60 * 15);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Mark used error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
