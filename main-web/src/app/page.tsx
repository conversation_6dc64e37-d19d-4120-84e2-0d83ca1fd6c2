import Image from "next/image";
import { authorizeAction } from "@/shared/lib/oauth/actions";

export default function Home() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md text-center">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">Main Web App</h1>
        <p className="text-gray-600 mb-8">Test PKCE OAuth 2.0 Flow</p>

        <form action={authorizeAction}>
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200"
          >
            Login with OAuth (PKCE)
          </button>
        </form>

        <div className="mt-8 text-sm text-gray-500">
          <p>This will redirect you to the Identity Provider (IDP)</p>
          <p>Running on: <code>localhost:5174</code></p>
          <p>IDP URL: <code>localhost:3002</code></p>
        </div>
      </div>
    </div>
  );
}
