import { authorizeAction } from "@/shared/lib/oauth/actions"
import { <PERSON><PERSON> } from "@/shared/uikit/button"

import { db, oauthStates } from "@/shared/db"

export const Header = async () => {

  const res = await db.select().from(oauthStates);

  console.log(res);

  return (

    <div className={"bg-slate-400 p-4 flex rounded-md"}>
      <h1 className={"text-2xl"}>БОЛЬШОЙ РЕБЕНОК</h1>
      <form className={"ml-auto"} action={authorizeAction}>
        <Button variant={"default"} type="submit">
          КНОПКА БАБЛА
        </Button>
      </form>
    </div>


  )
}